# 智能小车控制系统

## 项目概述
基于STM32F4的智能小车控制系统，支持循线、角度控制等多种运行模式。

## 最新修改记录

### 2024年速度优化 - 减少50%速度
为了提高小车运行的稳定性和安全性，对所有速度相关参数进行了50%的减速优化：

#### 修改的文件和参数：

**User/App/pid_app.c:**
- `V_L_MAX`: 120 → 60 (左电机最大输出限制)
- `V_R_MAX`: 120 → 60 (右电机最大输出限制)
- `basic_speed`: 80 → 40 (基础速度)
- 角度控制模式速度: 70 → 35
- 循线控制模式速度: 60 → 30
- 循线PID输出限制: ±80.0 → ±40.0
- 角度目标值: 35 → 17.5

**User/App/key_app.c (注释代码同步更新):**
- PID目标速度: 100 → 50, 90 → 45
- 电机直接控制速度: 100 → 50
- 速度增量: 5 → 2.5, 20 → 10

#### 技术说明：
1. **保持硬件层不变**: 电机驱动层的速度范围(-100.0 ~ +100.0)保持不变，确保硬件兼容性
2. **应用层统一减速**: 在PID控制层面统一将所有速度参数减少50%
3. **比例关系保持**: 所有速度参数按相同比例缩放，保持控制算法的相对关系不变
4. **注释代码同步**: 即使是注释掉的测试代码也进行了同步更新，保持代码一致性

#### 影响范围：
- 小车最大运行速度降低50%
- PID控制器输出范围相应调整
- 所有运行模式的速度都按比例降低
- 提高了系统稳定性和控制精度

## 系统架构

### 硬件层
- STM32F4微控制器
- DRV8871电机驱动器
- 编码器反馈
- 传感器模块(JY901S/BNO08X)

### 软件层
- 电机驱动层: PWM控制，支持快/慢衰减模式
- PID控制层: 速度环、循线环、角度环
- 应用层: 多模式控制逻辑
- 任务调度: 周期性任务管理

## 使用说明

### 编译和烧录
1. 使用Keil MDK-ARM打开项目
2. 编译项目
3. 通过ST-Link烧录到STM32F4

### 运行模式
- 模式0: 角度控制模式
- 模式1: 循线控制模式
- 模式3/4: 特殊角度控制模式

### 按键功能
- Key1: 启动PID控制
- Key2: LED控制
- Key3: 切换运行模式
- Key4: 预留功能

## 开发注意事项
1. 修改速度参数时请保持各参数间的比例关系
2. PID参数调整需要考虑新的速度范围
3. 测试时建议从低速开始逐步提高
4. 注意电机最小启动阈值的影响
