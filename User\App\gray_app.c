#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    // 检查GPIO状态
    my_printf(&huart1, "Gray Init: Checking GPIO...\r\n");
    my_printf(&huart1, "SCL Pin: PC4, SDA Pin: PC5\r\n");

    // 延时等待传感器稳定
    HAL_Delay(100);

    // 手动测试GPIO
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_4, GPIO_PIN_SET);  // SCL高
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_5, GPIO_PIN_SET);  // SDA高
    HAL_Delay(10);

    GPIO_PinState scl_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_4);
    GPIO_PinState sda_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_5);
    my_printf(&huart1, "GPIO Test: SCL=%d, SDA=%d\r\n", scl_state, sda_state);

    // 测试软件I2C连接
    my_printf(&huart1, "Testing Gray Sensor Connection...\r\n");
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");

        // 测试读取几次数据
        for(int i = 0; i < 3; i++) {
            uint8_t test_data = IIC_Get_Digtal();
            my_printf(&huart1, "Test read %d: 0x%02X\r\n", i+1, test_data);
            HAL_Delay(10);
        }
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");

        // 尝试重新连接
        HAL_Delay(50);
        if(Ping() == 0) {
            my_printf(&huart1, "Gray Sensor Reconnected!\r\n");
        } else {
            my_printf(&huart1, "Gray Sensor Still Failed!\r\n");
        }
    }
}

void Gray_Task(void)
{
//		HAL_NVIC_DisableIRQ(TIM2_IRQn);
		static uint32_t task_count = 0;
		uint8_t temp = 0;

		temp = IIC_Get_Digtal();
		if(temp == 0xAA)
		{
			// I2C通信错误，保持Digtal为0
			Digtal = 0;
			if(++task_count % 200 == 0) {  // 每1秒输出一次错误信息
				my_printf(&huart1, "Gray I2C Error: 0xAA\r\n");
			}
			return;
		}

    Digtal = ~temp;  // 取反处理

		// 减少调试输出频率
		if(++task_count % 100 == 0) {  // 每500ms输出一次
			my_printf(&huart1, "Gray Raw: 0x%02X, Processed: 0x%02X\r\n", temp, Digtal);
		}
	
//		HAL_NVIC_EnableIRQ(TIM2_IRQn); 
		
    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);  // 启用调试输出
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
}
